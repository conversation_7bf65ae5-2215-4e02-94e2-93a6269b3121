package com.example.demo.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * 万豪酒店地点查询响应类
 * 用于phoenixShopSuggestedPlacesQuery接口的响应数据
 */
public class PlaceQueryResponse {
    
    @JsonProperty("success")
    private Boolean success;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("data")
    private List<SuggestedPlace> data;
    
    @JsonProperty("total")
    private Integer total;
    
    @JsonProperty("query")
    private String query;
    
    @JsonProperty("timestamp")
    private Long timestamp;
    
    @JsonProperty("requestId")
    private String requestId;
    
    // 默认构造函数
    public PlaceQueryResponse() {
        this.timestamp = System.currentTimeMillis();
    }
    
    // 成功响应构造函数
    public PlaceQueryResponse(List<SuggestedPlace> data, String query) {
        this();
        this.success = true;
        this.message = "查询成功";
        this.data = data;
        this.total = data != null ? data.size() : 0;
        this.query = query;
    }
    
    // 错误响应构造函数
    public PlaceQueryResponse(String message) {
        this();
        this.success = false;
        this.message = message;
        this.total = 0;
    }
    
    // 全参构造函数
    public PlaceQueryResponse(Boolean success, String message, List<SuggestedPlace> data, 
                            Integer total, String query, Long timestamp, String requestId) {
        this.success = success;
        this.message = message;
        this.data = data;
        this.total = total;
        this.query = query;
        this.timestamp = timestamp;
        this.requestId = requestId;
    }
    
    // Getter和Setter方法
    public Boolean getSuccess() {
        return success;
    }
    
    public void setSuccess(Boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public List<SuggestedPlace> getData() {
        return data;
    }
    
    public void setData(List<SuggestedPlace> data) {
        this.data = data;
        this.total = data != null ? data.size() : 0;
    }
    
    public Integer getTotal() {
        return total;
    }
    
    public void setTotal(Integer total) {
        this.total = total;
    }
    
    public String getQuery() {
        return query;
    }
    
    public void setQuery(String query) {
        this.query = query;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    @Override
    public String toString() {
        return "PlaceQueryResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", total=" + total +
                ", query='" + query + '\'' +
                ", timestamp=" + timestamp +
                ", requestId='" + requestId + '\'' +
                '}';
    }
}
