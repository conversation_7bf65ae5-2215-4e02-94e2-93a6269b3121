package com.example.demo.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 万豪酒店建议地点实体类
 * 用于表示phoenixShopSuggestedPlacesQuery接口返回的地点信息
 */
public class SuggestedPlace {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("name")
    @NotBlank(message = "地点名称不能为空")
    private String name;
    
    @JsonProperty("type")
    private String type;
    
    @JsonProperty("city")
    private String city;
    
    @JsonProperty("country")
    private String country;
    
    @JsonProperty("latitude")
    private Double latitude;
    
    @JsonProperty("longitude")
    private Double longitude;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("imageUrl")
    private String imageUrl;
    
    @JsonProperty("rating")
    private Double rating;
    
    @JsonProperty("hotelCount")
    private Integer hotelCount;
    
    // 默认构造函数
    public SuggestedPlace() {}
    
    // 全参构造函数
    public SuggestedPlace(String id, String name, String type, String city, String country, 
                         Double latitude, Double longitude, String description, 
                         String imageUrl, Double rating, Integer hotelCount) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.city = city;
        this.country = country;
        this.latitude = latitude;
        this.longitude = longitude;
        this.description = description;
        this.imageUrl = imageUrl;
        this.rating = rating;
        this.hotelCount = hotelCount;
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getCity() {
        return city;
    }
    
    public void setCity(String city) {
        this.city = city;
    }
    
    public String getCountry() {
        return country;
    }
    
    public void setCountry(String country) {
        this.country = country;
    }
    
    public Double getLatitude() {
        return latitude;
    }
    
    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }
    
    public Double getLongitude() {
        return longitude;
    }
    
    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public Double getRating() {
        return rating;
    }
    
    public void setRating(Double rating) {
        this.rating = rating;
    }
    
    public Integer getHotelCount() {
        return hotelCount;
    }
    
    public void setHotelCount(Integer hotelCount) {
        this.hotelCount = hotelCount;
    }
    
    @Override
    public String toString() {
        return "SuggestedPlace{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", city='" + city + '\'' +
                ", country='" + country + '\'' +
                ", latitude=" + latitude +
                ", longitude=" + longitude +
                ", description='" + description + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", rating=" + rating +
                ", hotelCount=" + hotelCount +
                '}';
    }
}
