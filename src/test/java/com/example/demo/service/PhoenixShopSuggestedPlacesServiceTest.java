package com.example.demo.service;

import com.example.demo.model.PlaceQueryRequest;
import com.example.demo.model.PlaceQueryResponse;
import com.example.demo.model.SuggestedPlace;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * PhoenixShopSuggestedPlacesService 单元测试类
 * 测试万豪酒店地点建议查询服务的各种功能
 */
@ExtendWith(MockitoExtension.class)
class PhoenixShopSuggestedPlacesServiceTest {
    
    @Mock
    private WebClient.Builder webClientBuilder;
    
    @Mock
    private WebClient webClient;
    
    private PhoenixShopSuggestedPlacesService service;
    
    @BeforeEach
    void setUp() {
        when(webClientBuilder.baseUrl(any(String.class))).thenReturn(webClientBuilder);
        when(webClientBuilder.build()).thenReturn(webClient);
        service = new PhoenixShopSuggestedPlacesService(webClientBuilder);
    }
    
    @Test
    void testQuerySuggestedPlaces_WithBeijingQuery_ShouldReturnBeijingPlaces() {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("北京");
        request.setLimit(5);
        
        // When
        PlaceQueryResponse response = service.querySuggestedPlaces(request);
        
        // Then
        assertNotNull(response);
        assertTrue(response.getSuccess());
        assertEquals("查询成功", response.getMessage());
        assertEquals("北京", response.getQuery());
        assertNotNull(response.getData());
        assertFalse(response.getData().isEmpty());
        
        // 验证返回的地点包含北京相关信息
        SuggestedPlace firstPlace = response.getData().get(0);
        assertTrue(firstPlace.getName().contains("北京"));
        assertEquals("中国", firstPlace.getCountry());
        assertNotNull(firstPlace.getId());
        assertNotNull(firstPlace.getLatitude());
        assertNotNull(firstPlace.getLongitude());
    }
    
    @Test
    void testQuerySuggestedPlaces_WithShanghaiQuery_ShouldReturnShanghaiPlaces() {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("上海");
        request.setLimit(3);
        
        // When
        PlaceQueryResponse response = service.querySuggestedPlaces(request);
        
        // Then
        assertNotNull(response);
        assertTrue(response.getSuccess());
        assertEquals("上海", response.getQuery());
        assertNotNull(response.getData());
        
        // 验证返回的地点包含上海相关信息
        boolean hasShanghaiPlace = response.getData().stream()
                .anyMatch(place -> place.getName().contains("上海"));
        assertTrue(hasShanghaiPlace);
    }
    
    @Test
    void testQuerySuggestedPlaces_WithHongKongQuery_ShouldReturnHongKongPlaces() {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("香港");
        
        // When
        PlaceQueryResponse response = service.querySuggestedPlaces(request);
        
        // Then
        assertNotNull(response);
        assertTrue(response.getSuccess());
        assertEquals("香港", response.getQuery());
        assertNotNull(response.getData());
        
        // 验证香港地点信息
        SuggestedPlace hongKongPlace = response.getData().stream()
                .filter(place -> place.getName().contains("香港"))
                .findFirst()
                .orElse(null);
        
        assertNotNull(hongKongPlace);
        assertEquals("city", hongKongPlace.getType());
        assertEquals("中国", hongKongPlace.getCountry());
        assertTrue(hongKongPlace.getRating() > 0);
        assertTrue(hongKongPlace.getHotelCount() > 0);
    }
    
    @Test
    void testQuerySuggestedPlaces_WithUnknownQuery_ShouldReturnDefaultPlaces() {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("未知城市");
        
        // When
        PlaceQueryResponse response = service.querySuggestedPlaces(request);
        
        // Then
        assertNotNull(response);
        assertTrue(response.getSuccess());
        assertEquals("未知城市", response.getQuery());
        assertNotNull(response.getData());
        assertFalse(response.getData().isEmpty());
        
        // 应该返回默认的热门目的地
        SuggestedPlace defaultPlace = response.getData().get(0);
        assertEquals("热门目的地", defaultPlace.getName());
    }
    
    @Test
    void testQuerySuggestedPlaces_WithLimitParameter_ShouldRespectLimit() {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("北京");
        request.setLimit(1);
        
        // When
        PlaceQueryResponse response = service.querySuggestedPlaces(request);
        
        // Then
        assertNotNull(response);
        assertTrue(response.getSuccess());
        assertEquals(1, response.getData().size());
        assertEquals(1, response.getTotal());
    }
    
    @Test
    void testQuerySuggestedPlacesAsync_ShouldReturnMonoResponse() {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("广州");
        
        // When
        Mono<PlaceQueryResponse> responseMono = service.querySuggestedPlacesAsync(request);
        
        // Then
        StepVerifier.create(responseMono)
                .assertNext(response -> {
                    assertNotNull(response);
                    assertTrue(response.getSuccess());
                    assertEquals("广州", response.getQuery());
                    assertNotNull(response.getData());
                })
                .verifyComplete();
    }
    
    @Test
    void testValidateRequest_WithValidRequest_ShouldReturnTrue() {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("北京");
        request.setLimit(10);
        
        // When
        boolean isValid = service.validateRequest(request);
        
        // Then
        assertTrue(isValid);
    }
    
    @Test
    void testValidateRequest_WithNullRequest_ShouldReturnFalse() {
        // When
        boolean isValid = service.validateRequest(null);
        
        // Then
        assertFalse(isValid);
    }
    
    @Test
    void testValidateRequest_WithEmptyQuery_ShouldReturnFalse() {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("");
        
        // When
        boolean isValid = service.validateRequest(request);
        
        // Then
        assertFalse(isValid);
    }
    
    @Test
    void testValidateRequest_WithNullQuery_ShouldReturnFalse() {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest();
        request.setQuery(null);
        
        // When
        boolean isValid = service.validateRequest(request);
        
        // Then
        assertFalse(isValid);
    }
    
    @Test
    void testValidateRequest_WithNegativeLimit_ShouldReturnFalse() {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("北京");
        request.setLimit(-1);
        
        // When
        boolean isValid = service.validateRequest(request);
        
        // Then
        assertFalse(isValid);
    }
    
    @Test
    void testValidateRequest_WithZeroLimit_ShouldReturnFalse() {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("北京");
        request.setLimit(0);
        
        // When
        boolean isValid = service.validateRequest(request);
        
        // Then
        assertFalse(isValid);
    }
    
    @Test
    void testQuerySuggestedPlaces_ResponseShouldHaveTimestamp() {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("北京");
        long beforeCall = System.currentTimeMillis();
        
        // When
        PlaceQueryResponse response = service.querySuggestedPlaces(request);
        long afterCall = System.currentTimeMillis();
        
        // Then
        assertNotNull(response.getTimestamp());
        assertTrue(response.getTimestamp() >= beforeCall);
        assertTrue(response.getTimestamp() <= afterCall);
    }
    
    @Test
    void testQuerySuggestedPlaces_ResponseShouldHaveRequestId() {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("上海");
        
        // When
        PlaceQueryResponse response = service.querySuggestedPlaces(request);
        
        // Then
        assertNotNull(response.getRequestId());
        assertFalse(response.getRequestId().isEmpty());
    }
}
