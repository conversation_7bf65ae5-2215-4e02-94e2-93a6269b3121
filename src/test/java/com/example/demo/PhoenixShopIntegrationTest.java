package com.example.demo;

import com.example.demo.model.PlaceQueryRequest;
import com.example.demo.model.PlaceQueryResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Phoenix Shop 完整集成测试
 * 测试整个应用程序的端到端功能
 */
@SpringBootTest
@AutoConfigureWebMvc
class PhoenixShopIntegrationTest {
    
    @Autowired
    private WebApplicationContext webApplicationContext;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private MockMvc mockMvc;
    
    @Test
    void contextLoads() {
        // 测试Spring上下文是否正确加载
    }
    
    @Test
    void testFullWorkflow_BeijingQuery() throws Exception {
        // 设置MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("北京");
        request.setLimit(5);
        request.setLocale("zh-CN");
        request.setType("city");
        
        // When & Then - 测试POST接口
        mockMvc.perform(post("/mi/query/phoenixShopSuggestedPlacesQuery")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.query").value("北京"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].name").value("北京"))
                .andExpect(jsonPath("$.data[0].country").value("中国"))
                .andExpect(jsonPath("$.data[0].latitude").exists())
                .andExpect(jsonPath("$.data[0].longitude").exists())
                .andExpect(jsonPath("$.requestId").exists())
                .andExpect(jsonPath("$.timestamp").exists());
    }
    
    @Test
    void testFullWorkflow_ShanghaiQuery() throws Exception {
        // 设置MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // When & Then - 测试GET接口
        mockMvc.perform(get("/mi/query/phoenixShopSuggestedPlaces")
                .param("query", "上海")
                .param("limit", "3")
                .param("type", "city")
                .param("locale", "zh-CN"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.query").value("上海"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].name").value("上海"))
                .andExpect(jsonPath("$.data[0].country").value("中国"));
    }
    
    @Test
    void testFullWorkflow_HongKongQuery() throws Exception {
        // 设置MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("香港");
        request.setLimit(2);
        
        // When & Then - 测试异步接口
        mockMvc.perform(post("/mi/query/phoenixShopSuggestedPlacesQueryAsync")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());
    }
    
    @Test
    void testHealthEndpoint() throws Exception {
        // 设置MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // When & Then
        mockMvc.perform(get("/mi/query/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("Phoenix Shop Suggested Places Service is running"));
    }
    
    @Test
    void testErrorHandling_InvalidRequest() throws Exception {
        // 设置MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // Given - 空查询
        PlaceQueryRequest request = new PlaceQueryRequest("");
        
        // When & Then
        mockMvc.perform(post("/mi/query/phoenixShopSuggestedPlacesQuery")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
    
    @Test
    void testErrorHandling_MissingQueryParam() throws Exception {
        // 设置MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // When & Then
        mockMvc.perform(get("/mi/query/phoenixShopSuggestedPlaces"))
                .andExpect(status().isBadRequest());
    }
    
    @Test
    void testCrossOriginSupport() throws Exception {
        // 设置MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // When & Then
        mockMvc.perform(options("/mi/query/phoenixShopSuggestedPlacesQuery")
                .header("Origin", "https://www.marriott.com.cn")
                .header("Access-Control-Request-Method", "POST")
                .header("Access-Control-Request-Headers", "Content-Type"))
                .andExpect(status().isOk())
                .andExpect(header().string("Access-Control-Allow-Origin", "*"));
    }
}
