# 万豪酒店 Phoenix Shop API 演示脚本
Write-Host "=== 万豪酒店 Phoenix Shop Suggested Places API 演示 ===" -ForegroundColor Green
Write-Host ""

# 启动应用程序
Write-Host "正在启动应用程序..." -ForegroundColor Yellow
Start-Process -FilePath "cmd" -ArgumentList "/c", "mvnw.cmd spring-boot:run" -WindowStyle Minimized

# 等待应用程序启动
Write-Host "等待应用程序启动 (15秒)..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

$baseUrl = "http://localhost:8081"

# 测试健康检查
Write-Host "1. 测试健康检查接口" -ForegroundColor Cyan
try {
    $health = Invoke-RestMethod -Uri "$baseUrl/mi/query/health" -Method GET
    Write-Host "✓ 健康检查成功: $health" -ForegroundColor Green
} catch {
    Write-Host "✗ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
Write-Host ""

# 测试北京查询
Write-Host "2. 测试查询北京" -ForegroundColor Cyan
try {
    $beijing = Invoke-RestMethod -Uri "$baseUrl/mi/query/phoenixShopSuggestedPlaces?query=北京&limit=2" -Method GET
    Write-Host "✓ 北京查询成功:" -ForegroundColor Green
    Write-Host "  - 查询关键词: $($beijing.query)" -ForegroundColor White
    Write-Host "  - 返回结果数: $($beijing.total)" -ForegroundColor White
    Write-Host "  - 第一个地点: $($beijing.data[0].name)" -ForegroundColor White
} catch {
    Write-Host "✗ 北京查询失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试上海查询 (POST)
Write-Host "3. 测试查询上海 (POST接口)" -ForegroundColor Cyan
$postBody = @{
    query = "上海"
    limit = 3
    locale = "zh-CN"
    type = "city"
} | ConvertTo-Json

try {
    $shanghai = Invoke-RestMethod -Uri "$baseUrl/mi/query/phoenixShopSuggestedPlacesQuery" -Method POST -Body $postBody -ContentType "application/json"
    Write-Host "✓ 上海查询成功:" -ForegroundColor Green
    Write-Host "  - 查询关键词: $($shanghai.query)" -ForegroundColor White
    Write-Host "  - 返回结果数: $($shanghai.total)" -ForegroundColor White
    Write-Host "  - 请求ID: $($shanghai.requestId)" -ForegroundColor White
} catch {
    Write-Host "✗ 上海查询失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试香港查询
Write-Host "4. 测试查询香港" -ForegroundColor Cyan
$hkBody = @{
    query = "香港"
    limit = 2
    locale = "zh-CN"
} | ConvertTo-Json

try {
    $hongkong = Invoke-RestMethod -Uri "$baseUrl/mi/query/phoenixShopSuggestedPlacesQuery" -Method POST -Body $hkBody -ContentType "application/json"
    Write-Host "✓ 香港查询成功:" -ForegroundColor Green
    Write-Host "  - 查询关键词: $($hongkong.query)" -ForegroundColor White
    Write-Host "  - 返回结果数: $($hongkong.total)" -ForegroundColor White
    if ($hongkong.data.Count -gt 0) {
        Write-Host "  - 地点信息: $($hongkong.data[0].name) ($($hongkong.data[0].type))" -ForegroundColor White
        Write-Host "  - 酒店数量: $($hongkong.data[0].hotelCount)" -ForegroundColor White
        Write-Host "  - 评分: $($hongkong.data[0].rating)" -ForegroundColor White
    }
} catch {
    Write-Host "✗ 香港查询失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试错误处理
Write-Host "5. 测试错误处理 (空查询)" -ForegroundColor Cyan
$errorBody = @{
    query = ""
    limit = 5
} | ConvertTo-Json

try {
    $error = Invoke-RestMethod -Uri "$baseUrl/mi/query/phoenixShopSuggestedPlacesQuery" -Method POST -Body $errorBody -ContentType "application/json"
    Write-Host "✗ 错误处理测试失败: 应该返回错误但成功了" -ForegroundColor Red
} catch {
    Write-Host "✓ 错误处理测试成功: 正确返回了400错误" -ForegroundColor Green
}
Write-Host ""

Write-Host "=== API 演示完成 ===" -ForegroundColor Green
Write-Host ""
Write-Host "API 接口说明:" -ForegroundColor Yellow
Write-Host "- GET  /mi/query/health - 健康检查" -ForegroundColor White
Write-Host "- GET  /mi/query/phoenixShopSuggestedPlaces - 简化查询接口" -ForegroundColor White
Write-Host "- POST /mi/query/phoenixShopSuggestedPlacesQuery - 主要查询接口" -ForegroundColor White
Write-Host "- POST /mi/query/phoenixShopSuggestedPlacesQueryAsync - 异步查询接口" -ForegroundColor White
Write-Host ""
Write-Host "支持的查询关键词: 北京, 上海, 广州, 香港, 等" -ForegroundColor White
Write-Host "应用程序运行在: http://localhost:8081" -ForegroundColor White
