# Phoenix Shop Suggested Places Service

万豪酒店地点建议查询服务 - 模拟 `phoenixShopSuggestedPlacesQuery` 接口

## 项目概述

本项目是基于万豪酒店官网 (https://www.marriott.com.cn/default.mi) 中的 `phoenixShopSuggestedPlacesQuery` 接口创建的测试服务。由于原始接口需要认证且无法直接访问，本项目提供了一个完整的模拟实现，包括：

- 完整的REST API接口
- 数据模型定义
- 服务层逻辑
- 全面的单元测试和集成测试

## 技术栈

- **Java 17**
- **Spring Boot 3.5.3**
- **Spring Web** - REST API
- **Spring WebFlux** - 异步HTTP客户端
- **Jackson** - JSON处理
- **JUnit 5** - 单元测试
- **Mockito** - 模拟测试
- **Maven** - 项目管理

## 核心功能

### 数据分组规则
1. **字段排序**：对每条记录的原告、被告、第三人三个字段内容进行字典序排序
2. **分组逻辑**：按照排序后的"原告+被告+第三人"组合进行分组
3. **结果输出**：按组输出分组结果，包含每组的记录数量和详细信息

### 数据格式
输入数据为JSON格式，包含以下字段：
- `id`：记录唯一标识符
- `类型`：公告类型（开庭公告/法院公告）
- `原告`：诉讼发起方
- `被告`：诉讼被告方
- `第三人`：诉讼第三方
- `其他当事人`：其他参与方

## 技术架构

- **框架**：Spring Boot 3.5.3
- **Java版本**：17
- **构建工具**：Maven
- **主要依赖**：Spring Web, Jackson

## 项目结构

```
src/main/java/com/example/demo/
├── model/
│   ├── CourtAnnouncement.java      # 法院公告数据模型
│   └── GroupedResult.java          # 分组结果模型
├── service/
│   └── CourtAnnouncementService.java # 核心业务逻辑服务
├── controller/
│   └── CourtAnnouncementController.java # REST API控制器
└── Demo2Application.java           # 应用启动类

src/test/java/com/example/demo/
└── service/
    └── CourtAnnouncementServiceTest.java # 单元测试

src/main/resources/
└── sample-data.json                # 示例数据
```

## API接口

### 1. 数据分组处理
- **URL**: `POST /api/court-announcements/group`
- **功能**: 对法院公告数据进行分组处理
- **请求体**: JSON数组格式的法院公告数据
- **响应**: 分组处理结果

### 2. 统计信息获取
- **URL**: `POST /api/court-announcements/statistics`
- **功能**: 获取数据分组的统计信息
- **响应**: 包含总记录数、总分组数、平均每组记录数等统计信息

### 3. 数据验证
- **URL**: `POST /api/court-announcements/validate`
- **功能**: 验证输入数据的有效性
- **响应**: 验证结果和错误信息

### 4. 健康检查
- **URL**: `GET /api/court-announcements/health`
- **功能**: 检查服务状态
- **响应**: 服务健康状态信息

## 使用示例

### 启动应用
```bash
mvn spring-boot:run
```

### 调用分组API
```bash
curl -X POST http://localhost:8080/api/court-announcements/group \
  -H "Content-Type: application/json" \
  -d @src/main/resources/sample-data.json
```

### 示例响应
```json
{
  "success": true,
  "message": "数据分组处理成功",
  "data": [
    {
      "groupKey": "北京某科技有限公司+上海某贸易有限公司+广州某物流有限公司",
      "count": 2,
      "announcements": [...]
    }
  ],
  "totalGroups": 3,
  "totalRecords": 6
}
```

## 运行测试

```bash
mvn test
```

## 核心算法说明

### 分组键生成算法
```java
public String generateGroupKey() {
    // 将原告、被告、第三人放入数组
    String[] parties = {
        plaintiff != null ? plaintiff.trim() : "",
        defendant != null ? defendant.trim() : "",
        thirdParty != null ? thirdParty.trim() : ""
    };
    
    // 字典序排序
    Arrays.sort(parties);
    
    // 连接成分组键
    return String.join("+", parties);
}
```

### 分组处理流程
1. **数据验证**：检查输入数据的完整性和有效性
2. **分组键生成**：为每条记录生成排序后的分组键
3. **数据分组**：使用Stream API按分组键进行分组
4. **结果排序**：按分组键对结果进行排序
5. **统计计算**：计算每组的记录数量和总体统计信息

## 注意事项

1. **空值处理**：系统会自动处理空值和空字符串，确保分组逻辑的正确性
2. **字符串排序**：使用Java默认的字典序排序规则
3. **数据一致性**：相同当事人组合的记录会被分到同一组，无论原始数据中的顺序如何
4. **性能优化**：使用Stream API和LinkedHashMap保证处理效率和结果顺序

## 扩展功能

系统设计支持以下扩展：
- 自定义排序规则
- 多维度分组
- 数据导出功能
- 批量处理支持
