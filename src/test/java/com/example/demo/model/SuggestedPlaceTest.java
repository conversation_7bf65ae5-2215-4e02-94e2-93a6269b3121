package com.example.demo.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SuggestedPlace 模型类测试
 */
class SuggestedPlaceTest {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Test
    void testDefaultConstructor() {
        // When
        SuggestedPlace place = new SuggestedPlace();
        
        // Then
        assertNotNull(place);
        assertNull(place.getId());
        assertNull(place.getName());
        assertNull(place.getType());
    }
    
    @Test
    void testFullConstructor() {
        // Given
        String id = "1";
        String name = "北京";
        String type = "city";
        String city = "北京";
        String country = "中国";
        Double latitude = 39.9042;
        Double longitude = 116.4074;
        String description = "中国首都";
        String imageUrl = "https://example.com/beijing.jpg";
        Double rating = 4.8;
        Integer hotelCount = 25;
        
        // When
        SuggestedPlace place = new SuggestedPlace(id, name, type, city, country, 
                latitude, longitude, description, imageUrl, rating, hotelCount);
        
        // Then
        assertEquals(id, place.getId());
        assertEquals(name, place.getName());
        assertEquals(type, place.getType());
        assertEquals(city, place.getCity());
        assertEquals(country, place.getCountry());
        assertEquals(latitude, place.getLatitude());
        assertEquals(longitude, place.getLongitude());
        assertEquals(description, place.getDescription());
        assertEquals(imageUrl, place.getImageUrl());
        assertEquals(rating, place.getRating());
        assertEquals(hotelCount, place.getHotelCount());
    }
    
    @Test
    void testSettersAndGetters() {
        // Given
        SuggestedPlace place = new SuggestedPlace();
        
        // When
        place.setId("2");
        place.setName("上海");
        place.setType("city");
        place.setCity("上海");
        place.setCountry("中国");
        place.setLatitude(31.2304);
        place.setLongitude(121.4737);
        place.setDescription("国际化大都市");
        place.setImageUrl("https://example.com/shanghai.jpg");
        place.setRating(4.7);
        place.setHotelCount(32);
        
        // Then
        assertEquals("2", place.getId());
        assertEquals("上海", place.getName());
        assertEquals("city", place.getType());
        assertEquals("上海", place.getCity());
        assertEquals("中国", place.getCountry());
        assertEquals(31.2304, place.getLatitude());
        assertEquals(121.4737, place.getLongitude());
        assertEquals("国际化大都市", place.getDescription());
        assertEquals("https://example.com/shanghai.jpg", place.getImageUrl());
        assertEquals(4.7, place.getRating());
        assertEquals(32, place.getHotelCount());
    }
    
    @Test
    void testToString() {
        // Given
        SuggestedPlace place = new SuggestedPlace("1", "北京", "city", "北京", "中国", 
                39.9042, 116.4074, "中国首都", "https://example.com/beijing.jpg", 4.8, 25);
        
        // When
        String toString = place.toString();
        
        // Then
        assertNotNull(toString);
        assertTrue(toString.contains("北京"));
        assertTrue(toString.contains("city"));
        assertTrue(toString.contains("中国"));
        assertTrue(toString.contains("39.9042"));
        assertTrue(toString.contains("116.4074"));
    }
    
    @Test
    void testJsonSerialization() throws Exception {
        // Given
        SuggestedPlace place = new SuggestedPlace("1", "北京", "city", "北京", "中国", 
                39.9042, 116.4074, "中国首都", "https://example.com/beijing.jpg", 4.8, 25);
        
        // When
        String json = objectMapper.writeValueAsString(place);
        SuggestedPlace deserializedPlace = objectMapper.readValue(json, SuggestedPlace.class);
        
        // Then
        assertNotNull(json);
        assertTrue(json.contains("北京"));
        assertTrue(json.contains("city"));
        
        assertEquals(place.getId(), deserializedPlace.getId());
        assertEquals(place.getName(), deserializedPlace.getName());
        assertEquals(place.getType(), deserializedPlace.getType());
        assertEquals(place.getCity(), deserializedPlace.getCity());
        assertEquals(place.getCountry(), deserializedPlace.getCountry());
        assertEquals(place.getLatitude(), deserializedPlace.getLatitude());
        assertEquals(place.getLongitude(), deserializedPlace.getLongitude());
        assertEquals(place.getDescription(), deserializedPlace.getDescription());
        assertEquals(place.getImageUrl(), deserializedPlace.getImageUrl());
        assertEquals(place.getRating(), deserializedPlace.getRating());
        assertEquals(place.getHotelCount(), deserializedPlace.getHotelCount());
    }
}
