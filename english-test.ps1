# English API test script

# Create simple test data with English field names
$jsonString = @'
[
  {
    "id": "1",
    "类型": "开庭公告",
    "原告": "Company A",
    "被告": "Company B", 
    "第三人": "Company C",
    "其他当事人": "Company D"
  },
  {
    "id": "2",
    "类型": "法院公告",
    "原告": "Company C",
    "被告": "Company A",
    "第三人": "Company B",
    "其他当事人": "Company E"
  },
  {
    "id": "3",
    "类型": "开庭公告",
    "原告": "Company F",
    "被告": "Company G",
    "第三人": "Company H",
    "其他当事人": "Company I"
  }
]
'@

Write-Host "=== Test Data ===" -ForegroundColor Yellow
Write-Host $jsonString

Write-Host "`n=== Testing Group API ===" -ForegroundColor Green
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:8080/api/court-announcements/group' -Method POST -Body $jsonString -ContentType 'application/json; charset=utf-8'
    Write-Host "Status Code: $($response.StatusCode)"
    Write-Host "Response Content:"
    $responseObj = $response.Content | ConvertFrom-Json
    $responseObj | ConvertTo-Json -Depth 10
} catch {
    Write-Host "API test failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Error response: $responseBody" -ForegroundColor Red
    }
}
