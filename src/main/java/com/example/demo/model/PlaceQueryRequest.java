package com.example.demo.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

/**
 * 万豪酒店地点查询请求类
 * 用于phoenixShopSuggestedPlacesQuery接口的请求参数
 */
public class PlaceQueryRequest {
    
    @JsonProperty("query")
    @NotBlank(message = "查询关键词不能为空")
    private String query;
    
    @JsonProperty("locale")
    private String locale = "zh-CN";
    
    @JsonProperty("limit")
    @Min(value = 1, message = "返回结果数量必须大于0")
    private Integer limit = 10;
    
    @JsonProperty("type")
    private String type; // city, destination, hotel, etc.
    
    @JsonProperty("countryCode")
    private String countryCode;
    
    @JsonProperty("latitude")
    private Double latitude;
    
    @JsonProperty("longitude")
    private Double longitude;
    
    @JsonProperty("radius")
    private Double radius; // 搜索半径（公里）
    
    @JsonProperty("includeHotelCount")
    private Boolean includeHotelCount = true;
    
    // 默认构造函数
    public PlaceQueryRequest() {}
    
    // 主要构造函数
    public PlaceQueryRequest(String query) {
        this.query = query;
    }
    
    // 全参构造函数
    public PlaceQueryRequest(String query, String locale, Integer limit, String type, 
                           String countryCode, Double latitude, Double longitude, 
                           Double radius, Boolean includeHotelCount) {
        this.query = query;
        this.locale = locale;
        this.limit = limit;
        this.type = type;
        this.countryCode = countryCode;
        this.latitude = latitude;
        this.longitude = longitude;
        this.radius = radius;
        this.includeHotelCount = includeHotelCount;
    }
    
    // Getter和Setter方法
    public String getQuery() {
        return query;
    }
    
    public void setQuery(String query) {
        this.query = query;
    }
    
    public String getLocale() {
        return locale;
    }
    
    public void setLocale(String locale) {
        this.locale = locale;
    }
    
    public Integer getLimit() {
        return limit;
    }
    
    public void setLimit(Integer limit) {
        this.limit = limit;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getCountryCode() {
        return countryCode;
    }
    
    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
    
    public Double getLatitude() {
        return latitude;
    }
    
    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }
    
    public Double getLongitude() {
        return longitude;
    }
    
    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }
    
    public Double getRadius() {
        return radius;
    }
    
    public void setRadius(Double radius) {
        this.radius = radius;
    }
    
    public Boolean getIncludeHotelCount() {
        return includeHotelCount;
    }
    
    public void setIncludeHotelCount(Boolean includeHotelCount) {
        this.includeHotelCount = includeHotelCount;
    }
    
    @Override
    public String toString() {
        return "PlaceQueryRequest{" +
                "query='" + query + '\'' +
                ", locale='" + locale + '\'' +
                ", limit=" + limit +
                ", type='" + type + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", latitude=" + latitude +
                ", longitude=" + longitude +
                ", radius=" + radius +
                ", includeHotelCount=" + includeHotelCount +
                '}';
    }
}
