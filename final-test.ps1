# Final API test script

# Read the JSON data
$jsonData = Get-Content 'test-data.json' -Raw -Encoding UTF8

Write-Host "=== Testing Group API ===" -ForegroundColor Green
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:8080/api/court-announcements/group' -Method POST -Body $jsonData -ContentType 'application/json; charset=utf-8'
    Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response Content:" -ForegroundColor Green
    $responseObj = $response.Content | ConvertFrom-Json
    Write-Host ($responseObj | ConvertTo-Json -Depth 10) -ForegroundColor Cyan
} catch {
    Write-Host "API test failed: $($_.Exception.Message)" -ForegroundColor Red
}
