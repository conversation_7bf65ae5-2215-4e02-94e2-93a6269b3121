package com.example.demo.service;

import com.example.demo.model.CourtAnnouncement;
import com.example.demo.model.GroupedResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 法院公告服务测试类
 * 验证分组逻辑和数据处理的正确性
 */
@SpringBootTest
class CourtAnnouncementServiceTest {
    
    private CourtAnnouncementService service;
    
    @BeforeEach
    void setUp() {
        service = new CourtAnnouncementService();
    }
    
    /**
     * 测试基本分组功能
     * 验证相同当事人组合的记录能够正确分组
     */
    @Test
    void testBasicGrouping() {
        // 准备测试数据
        List<CourtAnnouncement> announcements = Arrays.asList(
            new CourtAnnouncement("1", "开庭公告", "张三", "李四", "王五", "其他1"),
            new CourtAnnouncement("2", "法院公告", "李四", "张三", "王五", "其他2"), // 顺序不同但应该分到同一组
            new CourtAnnouncement("3", "开庭公告", "赵六", "钱七", "孙八", "其他3")
        );
        
        // 执行分组
        List<GroupedResult> results = service.groupAnnouncements(announcements);
        
        // 验证结果
        assertEquals(2, results.size(), "应该分成2组");
        
        // 验证第一组（张三、李四、王五的组合）
        GroupedResult group1 = results.stream()
            .filter(r -> r.getGroupKey().contains("张三") && r.getGroupKey().contains("李四"))
            .findFirst()
            .orElse(null);
        assertNotNull(group1, "应该存在包含张三和李四的分组");
        assertEquals(2, group1.getCount(), "该组应该包含2条记录");
        
        // 验证第二组（赵六、钱七、孙八的组合）
        GroupedResult group2 = results.stream()
            .filter(r -> r.getGroupKey().contains("赵六"))
            .findFirst()
            .orElse(null);
        assertNotNull(group2, "应该存在包含赵六的分组");
        assertEquals(1, group2.getCount(), "该组应该包含1条记录");
    }
    
    /**
     * 测试分组键生成逻辑
     * 验证原告、被告、第三人字段排序后生成正确的分组键
     */
    @Test
    void testGroupKeyGeneration() {
        CourtAnnouncement announcement1 = new CourtAnnouncement("1", "开庭公告", "张三", "李四", "王五", "其他");
        CourtAnnouncement announcement2 = new CourtAnnouncement("2", "法院公告", "王五", "张三", "李四", "其他");
        
        String key1 = announcement1.generateGroupKey();
        String key2 = announcement2.generateGroupKey();
        
        assertEquals(key1, key2, "不同顺序的相同当事人应该生成相同的分组键");
        assertTrue(key1.contains("张三") && key1.contains("李四") && key1.contains("王五"), 
                  "分组键应该包含所有当事人");
    }
    
    /**
     * 测试空值处理
     * 验证当某些字段为空时的处理逻辑
     */
    @Test
    void testNullValueHandling() {
        List<CourtAnnouncement> announcements = Arrays.asList(
            new CourtAnnouncement("1", "开庭公告", "张三", null, "王五", "其他1"),
            new CourtAnnouncement("2", "法院公告", null, "李四", null, "其他2"),
            new CourtAnnouncement("3", "开庭公告", "", "李四", "王五", "其他3")
        );
        
        List<GroupedResult> results = service.groupAnnouncements(announcements);
        
        assertNotNull(results, "结果不应该为空");
        assertEquals(3, results.size(), "应该分成3组");
        
        // 验证每组都有正确的记录数
        results.forEach(group -> {
            assertTrue(group.getCount() > 0, "每组至少应该有1条记录");
            assertNotNull(group.getGroupKey(), "分组键不应该为空");
        });
    }
    
    /**
     * 测试统计信息功能
     */
    @Test
    void testStatistics() {
        List<CourtAnnouncement> announcements = Arrays.asList(
            new CourtAnnouncement("1", "开庭公告", "张三", "李四", "王五", "其他1"),
            new CourtAnnouncement("2", "法院公告", "张三", "李四", "王五", "其他2"),
            new CourtAnnouncement("3", "开庭公告", "赵六", "钱七", "孙八", "其他3")
        );
        
        Map<String, Object> statistics = service.getGroupStatistics(announcements);
        
        assertEquals(3, statistics.get("totalRecords"), "总记录数应该为3");
        assertEquals(2, statistics.get("totalGroups"), "总分组数应该为2");
        assertEquals(1.5, (Double) statistics.get("averageRecordsPerGroup"), 0.01, "平均每组记录数应该为1.5");
        
        @SuppressWarnings("unchecked")
        Map<String, Integer> groupSizes = (Map<String, Integer>) statistics.get("groupSizes");
        assertNotNull(groupSizes, "分组大小信息不应该为空");
        assertEquals(2, groupSizes.size(), "应该有2个分组的大小信息");
    }
    
    /**
     * 测试数据验证功能
     */
    @Test
    void testDataValidation() {
        // 测试有效数据
        List<CourtAnnouncement> validData = Arrays.asList(
            new CourtAnnouncement("1", "开庭公告", "张三", "李四", "王五", "其他1")
        );
        
        Map<String, Object> validResult = service.validateData(validData);
        assertTrue((Boolean) validResult.get("valid"), "有效数据应该通过验证");
        
        // 测试无效数据（缺少ID）
        List<CourtAnnouncement> invalidData = Arrays.asList(
            new CourtAnnouncement(null, "开庭公告", "张三", "李四", "王五", "其他1")
        );
        
        Map<String, Object> invalidResult = service.validateData(invalidData);
        assertFalse((Boolean) invalidResult.get("valid"), "无效数据应该验证失败");
        
        @SuppressWarnings("unchecked")
        List<String> errors = (List<String>) invalidResult.get("errors");
        assertFalse(errors.isEmpty(), "应该有错误信息");
    }
    
    /**
     * 测试空列表处理
     */
    @Test
    void testEmptyList() {
        List<GroupedResult> results = service.groupAnnouncements(Arrays.asList());
        
        assertNotNull(results, "结果不应该为null");
        assertTrue(results.isEmpty(), "空输入应该返回空结果");
    }
}
