server:
  port: 8081
  servlet:
    context-path: /

spring:
  application:
    name: phoenix-shop-service
  
  # JSON配置
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai

# 万豪API配置
marriott:
  api:
    base-url: https://www.marriott.com.cn/mi/query
    timeout: 30s
    retry-attempts: 3

# 日志配置
logging:
  level:
    com.example.demo: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
