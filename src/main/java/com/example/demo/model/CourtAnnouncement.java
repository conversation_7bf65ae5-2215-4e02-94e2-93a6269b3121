package com.example.demo.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Objects;

/**
 * 法院公告数据模型
 * 包含开庭公告和法院公告的统一数据格式
 */
public class CourtAnnouncement {
    
    /**
     * 公告ID - 唯一标识符
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 公告类型 - 开庭公告或法院公告
     */
    @JsonProperty("类型")
    private String type;
    
    /**
     * 原告信息 - 诉讼发起方
     */
    @JsonProperty("原告")
    private String plaintiff;
    
    /**
     * 被告信息 - 诉讼被告方
     */
    @JsonProperty("被告")
    private String defendant;
    
    /**
     * 第三人信息 - 诉讼第三方
     */
    @JsonProperty("第三人")
    private String thirdParty;
    
    /**
     * 其他当事人信息 - 除原告、被告、第三人外的其他参与方
     */
    @JsonProperty("其他当事人")
    private String otherParties;
    
    // 默认构造函数
    public CourtAnnouncement() {}
    
    // 全参构造函数
    public CourtAnnouncement(String id, String type, String plaintiff, String defendant, 
                           String thirdParty, String otherParties) {
        this.id = id;
        this.type = type;
        this.plaintiff = plaintiff;
        this.defendant = defendant;
        this.thirdParty = thirdParty;
        this.otherParties = otherParties;
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getPlaintiff() {
        return plaintiff;
    }
    
    public void setPlaintiff(String plaintiff) {
        this.plaintiff = plaintiff;
    }
    
    public String getDefendant() {
        return defendant;
    }
    
    public void setDefendant(String defendant) {
        this.defendant = defendant;
    }
    
    public String getThirdParty() {
        return thirdParty;
    }
    
    public void setThirdParty(String thirdParty) {
        this.thirdParty = thirdParty;
    }
    
    public String getOtherParties() {
        return otherParties;
    }
    
    public void setOtherParties(String otherParties) {
        this.otherParties = otherParties;
    }
    
    /**
     * 生成分组键 - 将原告、被告、第三人按字典序排序后连接
     * 用于数据分组的核心逻辑
     * @return 排序后的分组键字符串
     */
    public String generateGroupKey() {
        // 将三个字段放入数组进行排序
        String[] parties = {
            plaintiff != null ? plaintiff.trim() : "",
            defendant != null ? defendant.trim() : "",
            thirdParty != null ? thirdParty.trim() : ""
        };
        
        // 对数组进行字典序排序
        java.util.Arrays.sort(parties);
        
        // 连接排序后的字符串作为分组键
        return String.join("+", parties);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CourtAnnouncement that = (CourtAnnouncement) o;
        return Objects.equals(id, that.id) &&
               Objects.equals(type, that.type) &&
               Objects.equals(plaintiff, that.plaintiff) &&
               Objects.equals(defendant, that.defendant) &&
               Objects.equals(thirdParty, that.thirdParty) &&
               Objects.equals(otherParties, that.otherParties);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, type, plaintiff, defendant, thirdParty, otherParties);
    }
    
    @Override
    public String toString() {
        return "CourtAnnouncement{" +
               "id='" + id + '\'' +
               ", type='" + type + '\'' +
               ", plaintiff='" + plaintiff + '\'' +
               ", defendant='" + defendant + '\'' +
               ", thirdParty='" + thirdParty + '\'' +
               ", otherParties='" + otherParties + '\'' +
               '}';
    }
}
