# 简单的API测试脚本

# 创建简单的测试数据
$testData = @(
    @{
        id = "1"
        "类型" = "开庭公告"
        "原告" = "张三"
        "被告" = "李四"
        "第三人" = "王五"
        "其他当事人" = "赵六"
    },
    @{
        id = "2"
        "类型" = "法院公告"
        "原告" = "王五"
        "被告" = "张三"
        "第三人" = "李四"
        "其他当事人" = "钱七"
    },
    @{
        id = "3"
        "类型" = "开庭公告"
        "原告" = "孙八"
        "被告" = "周九"
        "第三人" = "吴十"
        "其他当事人" = "郑十一"
    }
)

# 转换为JSON
$jsonData = $testData | ConvertTo-Json -Depth 10

Write-Host "=== 测试数据 ===" -ForegroundColor Yellow
Write-Host $jsonData

Write-Host "`n=== 测试分组API ===" -ForegroundColor Green
try {
    $response = Invoke-WebRequest -Uri 'http://localhost:8080/api/court-announcements/group' -Method POST -Body $jsonData -ContentType 'application/json; charset=utf-8'
    Write-Host "状态码: $($response.StatusCode)"
    Write-Host "响应内容:"
    $responseObj = $response.Content | ConvertFrom-Json
    $responseObj | ConvertTo-Json -Depth 10
} catch {
    Write-Host "API测试失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误响应: $responseBody" -ForegroundColor Red
    }
}
