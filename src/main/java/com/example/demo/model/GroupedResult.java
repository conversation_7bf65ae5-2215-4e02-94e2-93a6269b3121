package com.example.demo.model;

import java.util.List;
import java.util.Objects;

/**
 * 分组结果数据模型
 * 用于表示按照原告+被告+第三人分组后的结果
 */
public class GroupedResult {
    
    /**
     * 分组键 - 由原告、被告、第三人排序后组成的唯一标识
     */
    private String groupKey;
    
    /**
     * 该分组下的所有法院公告记录
     */
    private List<CourtAnnouncement> announcements;
    
    /**
     * 该分组下的记录数量
     */
    private int count;
    
    // 默认构造函数
    public GroupedResult() {}
    
    // 全参构造函数
    public GroupedResult(String groupKey, List<CourtAnnouncement> announcements) {
        this.groupKey = groupKey;
        this.announcements = announcements;
        this.count = announcements != null ? announcements.size() : 0;
    }
    
    // Getter和Setter方法
    public String getGroupKey() {
        return groupKey;
    }
    
    public void setGroupKey(String groupKey) {
        this.groupKey = groupKey;
    }
    
    public List<CourtAnnouncement> getAnnouncements() {
        return announcements;
    }
    
    public void setAnnouncements(List<CourtAnnouncement> announcements) {
        this.announcements = announcements;
        this.count = announcements != null ? announcements.size() : 0;
    }
    
    public int getCount() {
        return count;
    }
    
    public void setCount(int count) {
        this.count = count;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GroupedResult that = (GroupedResult) o;
        return count == that.count &&
               Objects.equals(groupKey, that.groupKey) &&
               Objects.equals(announcements, that.announcements);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(groupKey, announcements, count);
    }
    
    @Override
    public String toString() {
        return "GroupedResult{" +
               "groupKey='" + groupKey + '\'' +
               ", count=" + count +
               ", announcements=" + announcements +
               '}';
    }
}
