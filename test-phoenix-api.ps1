# Phoenix Shop Suggested Places API 测试脚本
# 测试万豪酒店地点建议查询接口

Write-Host "=== Phoenix Shop Suggested Places API 测试 ===" -ForegroundColor Green
Write-Host ""

$baseUrl = "http://localhost:8081"

# 测试健康检查接口
Write-Host "1. 测试健康检查接口" -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/mi/query/health" -Method GET
    Write-Host "健康检查响应: $healthResponse" -ForegroundColor Green
} catch {
    Write-Host "健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试GET接口 - 北京查询
Write-Host "2. 测试GET接口 - 查询北京" -ForegroundColor Yellow
try {
    $getResponse = Invoke-RestMethod -Uri "$baseUrl/mi/query/phoenixShopSuggestedPlaces?query=北京&limit=3" -Method GET
    Write-Host "GET响应:" -ForegroundColor Green
    $getResponse | ConvertTo-Json -Depth 10 | Write-Host
} catch {
    Write-Host "GET请求失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试POST接口 - 上海查询
Write-Host "3. 测试POST接口 - 查询上海" -ForegroundColor Yellow
$postBody = @{
    query = "上海"
    limit = 5
    locale = "zh-CN"
    type = "city"
    includeHotelCount = $true
} | ConvertTo-Json

try {
    $postResponse = Invoke-RestMethod -Uri "$baseUrl/mi/query/phoenixShopSuggestedPlacesQuery" -Method POST -Body $postBody -ContentType "application/json"
    Write-Host "POST响应:" -ForegroundColor Green
    $postResponse | ConvertTo-Json -Depth 10 | Write-Host
} catch {
    Write-Host "POST请求失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试POST接口 - 香港查询
Write-Host "4. 测试POST接口 - 查询香港" -ForegroundColor Yellow
$hkBody = @{
    query = "香港"
    limit = 2
    locale = "zh-CN"
    type = "city"
} | ConvertTo-Json

try {
    $hkResponse = Invoke-RestMethod -Uri "$baseUrl/mi/query/phoenixShopSuggestedPlacesQuery" -Method POST -Body $hkBody -ContentType "application/json"
    Write-Host "香港查询响应:" -ForegroundColor Green
    $hkResponse | ConvertTo-Json -Depth 10 | Write-Host
} catch {
    Write-Host "香港查询失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试异步接口
Write-Host "5. 测试异步接口 - 查询广州" -ForegroundColor Yellow
$asyncBody = @{
    query = "广州"
    limit = 3
    locale = "zh-CN"
} | ConvertTo-Json

try {
    $asyncResponse = Invoke-RestMethod -Uri "$baseUrl/mi/query/phoenixShopSuggestedPlacesQueryAsync" -Method POST -Body $asyncBody -ContentType "application/json"
    Write-Host "异步查询响应:" -ForegroundColor Green
    $asyncResponse | ConvertTo-Json -Depth 10 | Write-Host
} catch {
    Write-Host "异步查询失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试错误处理 - 空查询
Write-Host "6. 测试错误处理 - 空查询" -ForegroundColor Yellow
$errorBody = @{
    query = ""
    limit = 5
} | ConvertTo-Json

try {
    $errorResponse = Invoke-RestMethod -Uri "$baseUrl/mi/query/phoenixShopSuggestedPlacesQuery" -Method POST -Body $errorBody -ContentType "application/json"
    Write-Host "错误测试响应:" -ForegroundColor Green
    $errorResponse | ConvertTo-Json -Depth 10 | Write-Host
} catch {
    Write-Host "预期的错误响应: $($_.Exception.Message)" -ForegroundColor Yellow
}
Write-Host ""

Write-Host "=== API 测试完成 ===" -ForegroundColor Green
