package com.example.demo.controller;

import com.example.demo.model.PlaceQueryRequest;
import com.example.demo.model.PlaceQueryResponse;
import com.example.demo.service.PhoenixShopSuggestedPlacesService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import reactor.core.publisher.Mono;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * PhoenixShopController 集成测试类
 * 测试万豪酒店Phoenix Shop控制器的REST API接口
 */
@WebMvcTest(PhoenixShopController.class)
class PhoenixShopControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PhoenixShopSuggestedPlacesService suggestedPlacesService;

    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    void testPhoenixShopSuggestedPlacesQuery_WithValidRequest_ShouldReturnSuccess() throws Exception {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("北京");
        request.setLimit(5);
        
        PlaceQueryResponse mockResponse = new PlaceQueryResponse(Collections.emptyList(), "北京");
        mockResponse.setRequestId("test-request-id");
        
        when(suggestedPlacesService.validateRequest(any())).thenReturn(true);
        when(suggestedPlacesService.querySuggestedPlaces(any())).thenReturn(mockResponse);
        
        // When & Then
        mockMvc.perform(post("/mi/query/phoenixShopSuggestedPlacesQuery")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("查询成功"))
                .andExpect(jsonPath("$.query").value("北京"))
                .andExpect(jsonPath("$.requestId").value("test-request-id"))
                .andExpect(jsonPath("$.data").isArray());
    }
    
    @Test
    void testPhoenixShopSuggestedPlacesQuery_WithInvalidRequest_ShouldReturnBadRequest() throws Exception {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("");
        
        when(suggestedPlacesService.validateRequest(any())).thenReturn(false);
        
        // When & Then
        mockMvc.perform(post("/mi/query/phoenixShopSuggestedPlacesQuery")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
    
    @Test
    void testPhoenixShopSuggestedPlacesQuery_WithServiceError_ShouldReturnInternalServerError() throws Exception {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("北京");
        
        PlaceQueryResponse errorResponse = new PlaceQueryResponse("服务异常");
        
        when(suggestedPlacesService.validateRequest(any())).thenReturn(true);
        when(suggestedPlacesService.querySuggestedPlaces(any())).thenReturn(errorResponse);
        
        // When & Then
        mockMvc.perform(post("/mi/query/phoenixShopSuggestedPlacesQuery")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("服务异常"));
    }
    
    @Test
    void testPhoenixShopSuggestedPlacesQueryAsync_WithValidRequest_ShouldReturnSuccess() throws Exception {
        // Given
        PlaceQueryRequest request = new PlaceQueryRequest("上海");
        
        PlaceQueryResponse mockResponse = new PlaceQueryResponse(Collections.emptyList(), "上海");
        
        when(suggestedPlacesService.validateRequest(any())).thenReturn(true);
        when(suggestedPlacesService.querySuggestedPlacesAsync(any())).thenReturn(Mono.just(mockResponse));
        
        // When & Then
        mockMvc.perform(post("/mi/query/phoenixShopSuggestedPlacesQueryAsync")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());
    }
    
    @Test
    void testPhoenixShopSuggestedPlaces_GetMethod_WithValidParams_ShouldReturnSuccess() throws Exception {
        // Given
        PlaceQueryResponse mockResponse = new PlaceQueryResponse(Collections.emptyList(), "广州");
        
        when(suggestedPlacesService.validateRequest(any())).thenReturn(true);
        when(suggestedPlacesService.querySuggestedPlaces(any())).thenReturn(mockResponse);
        
        // When & Then
        mockMvc.perform(get("/mi/query/phoenixShopSuggestedPlaces")
                .param("query", "广州")
                .param("limit", "5")
                .param("type", "city")
                .param("locale", "zh-CN"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.query").value("广州"));
    }
    
    @Test
    void testPhoenixShopSuggestedPlaces_GetMethod_WithDefaultParams_ShouldReturnSuccess() throws Exception {
        // Given
        PlaceQueryResponse mockResponse = new PlaceQueryResponse(Collections.emptyList(), "深圳");
        
        when(suggestedPlacesService.validateRequest(any())).thenReturn(true);
        when(suggestedPlacesService.querySuggestedPlaces(any())).thenReturn(mockResponse);
        
        // When & Then
        mockMvc.perform(get("/mi/query/phoenixShopSuggestedPlaces")
                .param("query", "深圳"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.query").value("深圳"));
    }
    
    @Test
    void testPhoenixShopSuggestedPlaces_GetMethod_WithInvalidQuery_ShouldReturnBadRequest() throws Exception {
        // Given
        when(suggestedPlacesService.validateRequest(any())).thenReturn(false);
        
        // When & Then
        mockMvc.perform(get("/mi/query/phoenixShopSuggestedPlaces")
                .param("query", ""))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("请求参数无效"));
    }
    
    @Test
    void testHealth_ShouldReturnServiceStatus() throws Exception {
        // When & Then
        mockMvc.perform(get("/mi/query/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("Phoenix Shop Suggested Places Service is running"));
    }
    
    @Test
    void testPhoenixShopSuggestedPlacesQuery_WithMissingRequestBody_ShouldReturnBadRequest() throws Exception {
        // When & Then
        mockMvc.perform(post("/mi/query/phoenixShopSuggestedPlacesQuery")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }
    
    @Test
    void testPhoenixShopSuggestedPlacesQuery_WithInvalidJson_ShouldReturnBadRequest() throws Exception {
        // When & Then
        mockMvc.perform(post("/mi/query/phoenixShopSuggestedPlacesQuery")
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid json"))
                .andExpect(status().isBadRequest());
    }
    
    @Test
    void testPhoenixShopSuggestedPlaces_GetMethod_WithMissingQueryParam_ShouldReturnBadRequest() throws Exception {
        // When & Then
        mockMvc.perform(get("/mi/query/phoenixShopSuggestedPlaces"))
                .andExpect(status().isBadRequest());
    }
}
