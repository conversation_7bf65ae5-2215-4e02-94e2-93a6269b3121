package com.example.demo.service;

import com.example.demo.model.CourtAnnouncement;
import com.example.demo.model.GroupedResult;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 法院公告数据处理服务
 * 实现核心业务逻辑：数据分组和排序
 */
@Service
public class CourtAnnouncementService {
    
    /**
     * 对法院公告数据进行分组处理
     * 核心逻辑：
     * 1. 对每条记录的原告、被告、第三人字段内容进行排序
     * 2. 按照排序后的"原告+被告+第三人"进行分组
     * 3. 返回分组结果
     * 
     * @param announcements 输入的法院公告数据列表
     * @return 按分组键组织的结果列表
     */
    public List<GroupedResult> groupAnnouncements(List<CourtAnnouncement> announcements) {
        if (announcements == null || announcements.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 使用Stream API进行分组处理
        Map<String, List<CourtAnnouncement>> groupedMap = announcements.stream()
                // 过滤掉空记录
                .filter(Objects::nonNull)
                // 按照生成的分组键进行分组
                .collect(Collectors.groupingBy(
                    CourtAnnouncement::generateGroupKey,
                    // 使用LinkedHashMap保持插入顺序
                    LinkedHashMap::new,
                    Collectors.toList()
                ));
        
        // 将Map转换为GroupedResult列表
        return groupedMap.entrySet().stream()
                .map(entry -> new GroupedResult(entry.getKey(), entry.getValue()))
                // 按分组键排序，确保输出结果的一致性
                .sorted(Comparator.comparing(GroupedResult::getGroupKey))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取分组统计信息
     * 
     * @param announcements 输入的法院公告数据列表
     * @return 包含统计信息的Map
     */
    public Map<String, Object> getGroupStatistics(List<CourtAnnouncement> announcements) {
        Map<String, Object> statistics = new HashMap<>();
        
        if (announcements == null || announcements.isEmpty()) {
            statistics.put("totalRecords", 0);
            statistics.put("totalGroups", 0);
            statistics.put("averageRecordsPerGroup", 0.0);
            return statistics;
        }
        
        List<GroupedResult> groupedResults = groupAnnouncements(announcements);
        
        statistics.put("totalRecords", announcements.size());
        statistics.put("totalGroups", groupedResults.size());
        statistics.put("averageRecordsPerGroup", 
                      announcements.size() / (double) groupedResults.size());
        
        // 添加每个分组的记录数量分布
        Map<String, Integer> groupSizes = groupedResults.stream()
                .collect(Collectors.toMap(
                    GroupedResult::getGroupKey,
                    GroupedResult::getCount
                ));
        statistics.put("groupSizes", groupSizes);
        
        return statistics;
    }
    
    /**
     * 验证输入数据的有效性
     * 
     * @param announcements 待验证的数据列表
     * @return 验证结果和错误信息
     */
    public Map<String, Object> validateData(List<CourtAnnouncement> announcements) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        
        if (announcements == null) {
            errors.add("输入数据不能为空");
            result.put("valid", false);
            result.put("errors", errors);
            return result;
        }
        
        for (int i = 0; i < announcements.size(); i++) {
            CourtAnnouncement announcement = announcements.get(i);
            if (announcement == null) {
                errors.add("第" + (i + 1) + "条记录为空");
                continue;
            }
            
            // 检查必要字段
            if (announcement.getId() == null || announcement.getId().trim().isEmpty()) {
                errors.add("第" + (i + 1) + "条记录缺少ID字段");
            }
            
            // 检查分组相关字段是否都为空
            if ((announcement.getPlaintiff() == null || announcement.getPlaintiff().trim().isEmpty()) &&
                (announcement.getDefendant() == null || announcement.getDefendant().trim().isEmpty()) &&
                (announcement.getThirdParty() == null || announcement.getThirdParty().trim().isEmpty())) {
                errors.add("第" + (i + 1) + "条记录的原告、被告、第三人字段都为空，无法进行分组");
            }
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("totalRecords", announcements.size());
        
        return result;
    }
}
