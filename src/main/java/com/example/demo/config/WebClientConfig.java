package com.example.demo.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * WebClient配置类
 * 用于配置HTTP客户端
 */
@Configuration
public class WebClientConfig {
    
    @Bean
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder()
                .defaultHeader("User-Agent", "Phoenix-Shop-Service/1.0")
                .defaultHeader("Accept", "application/json")
                .defaultHeader("Content-Type", "application/json");
    }
}
