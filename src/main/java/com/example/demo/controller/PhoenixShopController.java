package com.example.demo.controller;

import com.example.demo.model.PlaceQueryRequest;
import com.example.demo.model.PlaceQueryResponse;
import com.example.demo.service.PhoenixShopSuggestedPlacesService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * 万豪酒店Phoenix Shop控制器
 * 提供phoenixShopSuggestedPlacesQuery接口的REST API
 */
@RestController
@RequestMapping("/mi/query")
@CrossOrigin(origins = "*")
public class PhoenixShopController {
    
    private final PhoenixShopSuggestedPlacesService suggestedPlacesService;
    
    @Autowired
    public PhoenixShopController(PhoenixShopSuggestedPlacesService suggestedPlacesService) {
        this.suggestedPlacesService = suggestedPlacesService;
    }
    
    /**
     * 万豪酒店地点建议查询接口
     * 模拟 https://www.marriott.com.cn/mi/query/phoenixShopSuggestedPlacesQuery
     * 
     * @param request 查询请求
     * @return 查询响应
     */
    @PostMapping("/phoenixShopSuggestedPlacesQuery")
    public ResponseEntity<PlaceQueryResponse> phoenixShopSuggestedPlacesQuery(
            @Valid @RequestBody PlaceQueryRequest request) {
        
        try {
            // 验证请求参数
            if (!suggestedPlacesService.validateRequest(request)) {
                PlaceQueryResponse errorResponse = new PlaceQueryResponse("请求参数无效");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            // 执行查询
            PlaceQueryResponse response = suggestedPlacesService.querySuggestedPlaces(request);
            
            if (response.getSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
            }
            
        } catch (Exception e) {
            PlaceQueryResponse errorResponse = new PlaceQueryResponse("服务器内部错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 异步版本的地点建议查询接口
     * 
     * @param request 查询请求
     * @return 异步查询响应
     */
    @PostMapping("/phoenixShopSuggestedPlacesQueryAsync")
    public Mono<ResponseEntity<PlaceQueryResponse>> phoenixShopSuggestedPlacesQueryAsync(
            @Valid @RequestBody PlaceQueryRequest request) {
        
        if (!suggestedPlacesService.validateRequest(request)) {
            PlaceQueryResponse errorResponse = new PlaceQueryResponse("请求参数无效");
            return Mono.just(ResponseEntity.badRequest().body(errorResponse));
        }
        
        return suggestedPlacesService.querySuggestedPlacesAsync(request)
                .map(response -> {
                    if (response.getSuccess()) {
                        return ResponseEntity.ok(response);
                    } else {
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
                    }
                })
                .onErrorReturn(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new PlaceQueryResponse("服务器内部错误")));
    }
    
    /**
     * GET方式的简化查询接口
     * 
     * @param query 查询关键词
     * @param limit 返回数量限制
     * @param type 地点类型
     * @param locale 语言环境
     * @return 查询响应
     */
    @GetMapping("/phoenixShopSuggestedPlaces")
    public ResponseEntity<PlaceQueryResponse> phoenixShopSuggestedPlaces(
            @RequestParam String query,
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "zh-CN") String locale) {
        
        try {
            PlaceQueryRequest request = new PlaceQueryRequest();
            request.setQuery(query);
            request.setLimit(limit);
            request.setType(type);
            request.setLocale(locale);
            
            if (!suggestedPlacesService.validateRequest(request)) {
                PlaceQueryResponse errorResponse = new PlaceQueryResponse("请求参数无效");
                return ResponseEntity.badRequest().body(errorResponse);
            }
            
            PlaceQueryResponse response = suggestedPlacesService.querySuggestedPlaces(request);
            
            if (response.getSuccess()) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
            }
            
        } catch (Exception e) {
            PlaceQueryResponse errorResponse = new PlaceQueryResponse("服务器内部错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * 健康检查接口
     * 
     * @return 服务状态
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("Phoenix Shop Suggested Places Service is running");
    }
}
