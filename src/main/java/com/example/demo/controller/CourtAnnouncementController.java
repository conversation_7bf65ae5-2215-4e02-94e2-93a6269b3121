package com.example.demo.controller;

import com.example.demo.model.CourtAnnouncement;
import com.example.demo.model.GroupedResult;
import com.example.demo.service.CourtAnnouncementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 法院公告数据处理控制器
 * 提供RESTful API接口用于处理法院公告数据的分组功能
 */
@RestController
@RequestMapping("/api/court-announcements")
@CrossOrigin(origins = "*") // 允许跨域访问
public class CourtAnnouncementController {
    
    @Autowired
    private CourtAnnouncementService courtAnnouncementService;
    
    /**
     * 对法院公告数据进行分组处理
     * 
     * 接收JSON格式的法院公告数据列表，按照以下规则进行处理：
     * 1. 对每条记录的原告、被告、第三人字段内容进行排序
     * 2. 按照排序后的"原告+被告+第三人"进行分组
     * 3. 返回分组后的结果
     * 
     * @param announcements 法院公告数据列表
     * @return 分组处理结果
     */
    @PostMapping("/group")
    public ResponseEntity<?> groupAnnouncements(@RequestBody List<CourtAnnouncement> announcements) {
        try {
            // 数据验证
            Map<String, Object> validation = courtAnnouncementService.validateData(announcements);
            if (!(Boolean) validation.get("valid")) {
                return ResponseEntity.badRequest().body(validation);
            }
            
            // 执行分组处理
            List<GroupedResult> groupedResults = courtAnnouncementService.groupAnnouncements(announcements);
            
            // 构建响应结果
            Map<String, Object> response = Map.of(
                "success", true,
                "message", "数据分组处理成功",
                "data", groupedResults,
                "totalGroups", groupedResults.size(),
                "totalRecords", announcements.size()
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            // 异常处理
            Map<String, Object> errorResponse = Map.of(
                "success", false,
                "message", "数据处理失败: " + e.getMessage(),
                "error", e.getClass().getSimpleName()
            );
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 获取分组统计信息
     * 
     * @param announcements 法院公告数据列表
     * @return 统计信息
     */
    @PostMapping("/statistics")
    public ResponseEntity<?> getStatistics(@RequestBody List<CourtAnnouncement> announcements) {
        try {
            Map<String, Object> statistics = courtAnnouncementService.getGroupStatistics(announcements);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "message", "统计信息获取成功",
                "data", statistics
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, Object> errorResponse = Map.of(
                "success", false,
                "message", "统计信息获取失败: " + e.getMessage(),
                "error", e.getClass().getSimpleName()
            );
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 数据验证接口
     * 
     * @param announcements 待验证的数据
     * @return 验证结果
     */
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateData(@RequestBody List<CourtAnnouncement> announcements) {
        Map<String, Object> validation = courtAnnouncementService.validateData(announcements);
        return ResponseEntity.ok(validation);
    }
    
    /**
     * 健康检查接口
     * 
     * @return 服务状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = Map.of(
            "status", "UP",
            "service", "CourtAnnouncementService",
            "timestamp", System.currentTimeMillis()
        );
        return ResponseEntity.ok(health);
    }
}
