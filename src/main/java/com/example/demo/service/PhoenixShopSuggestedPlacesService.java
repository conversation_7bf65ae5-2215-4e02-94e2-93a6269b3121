package com.example.demo.service;

import com.example.demo.model.PlaceQueryRequest;
import com.example.demo.model.PlaceQueryResponse;
import com.example.demo.model.SuggestedPlace;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 万豪酒店地点建议查询服务
 * 模拟phoenixShopSuggestedPlacesQuery接口的功能
 */
@Service
public class PhoenixShopSuggestedPlacesService {
    
    private final WebClient webClient;
    private static final String MARRIOTT_API_BASE_URL = "https://www.marriott.com.cn/mi/query";
    
    public PhoenixShopSuggestedPlacesService(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.baseUrl(MARRIOTT_API_BASE_URL).build();
    }
    
    /**
     * 查询建议地点
     * @param request 查询请求
     * @return 查询响应
     */
    public PlaceQueryResponse querySuggestedPlaces(PlaceQueryRequest request) {
        try {
            // 由于实际API需要认证，这里使用模拟数据
            List<SuggestedPlace> mockData = generateMockData(request);
            
            PlaceQueryResponse response = new PlaceQueryResponse(mockData, request.getQuery());
            response.setRequestId(UUID.randomUUID().toString());
            
            return response;
        } catch (Exception e) {
            return new PlaceQueryResponse("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 异步查询建议地点
     * @param request 查询请求
     * @return 异步查询响应
     */
    public Mono<PlaceQueryResponse> querySuggestedPlacesAsync(PlaceQueryRequest request) {
        return Mono.fromCallable(() -> querySuggestedPlaces(request));
    }
    
    /**
     * 调用真实的万豪API（需要认证）
     * @param request 查询请求
     * @return 异步查询响应
     */
    public Mono<PlaceQueryResponse> callRealMarriottAPI(PlaceQueryRequest request) {
        return webClient.post()
                .uri("/phoenixShopSuggestedPlacesQuery")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(PlaceQueryResponse.class)
                .onErrorReturn(new PlaceQueryResponse("API调用失败，可能需要认证"));
    }
    
    /**
     * 生成模拟数据
     * @param request 查询请求
     * @return 模拟的建议地点列表
     */
    private List<SuggestedPlace> generateMockData(PlaceQueryRequest request) {
        List<SuggestedPlace> places = new ArrayList<>();
        String query = request.getQuery().toLowerCase();
        
        // 根据查询关键词生成相应的模拟数据
        if (query.contains("北京") || query.contains("beijing")) {
            places.add(new SuggestedPlace("1", "北京", "city", "北京", "中国", 
                    39.9042, 116.4074, "中国首都，历史文化名城", 
                    "https://example.com/beijing.jpg", 4.8, 25));
            places.add(new SuggestedPlace("2", "北京王府井", "destination", "北京", "中国", 
                    39.9097, 116.4180, "著名商业街区", 
                    "https://example.com/wangfujing.jpg", 4.6, 8));
        } else if (query.contains("上海") || query.contains("shanghai")) {
            places.add(new SuggestedPlace("3", "上海", "city", "上海", "中国", 
                    31.2304, 121.4737, "国际化大都市", 
                    "https://example.com/shanghai.jpg", 4.7, 32));
            places.add(new SuggestedPlace("4", "上海外滩", "destination", "上海", "中国", 
                    31.2396, 121.4906, "著名景点和商务区", 
                    "https://example.com/bund.jpg", 4.9, 12));
        } else if (query.contains("广州") || query.contains("guangzhou")) {
            places.add(new SuggestedPlace("5", "广州", "city", "广州", "中国", 
                    23.1291, 113.2644, "南方重要城市", 
                    "https://example.com/guangzhou.jpg", 4.5, 18));
        } else if (query.contains("香港") || query.contains("hong kong")) {
            places.add(new SuggestedPlace("6", "香港", "city", "香港", "中国", 
                    22.3193, 114.1694, "国际金融中心", 
                    "https://example.com/hongkong.jpg", 4.8, 28));
        } else {
            // 默认返回一些通用地点
            places.add(new SuggestedPlace("7", "热门目的地", "destination", "全球", "全球", 
                    0.0, 0.0, "万豪酒店热门目的地", 
                    "https://example.com/popular.jpg", 4.5, 100));
        }
        
        // 根据limit限制返回数量
        int limit = request.getLimit() != null ? request.getLimit() : 10;
        return places.subList(0, Math.min(places.size(), limit));
    }
    
    /**
     * 验证查询请求
     * @param request 查询请求
     * @return 是否有效
     */
    public boolean validateRequest(PlaceQueryRequest request) {
        if (request == null) {
            return false;
        }
        if (request.getQuery() == null || request.getQuery().trim().isEmpty()) {
            return false;
        }
        if (request.getLimit() != null && request.getLimit() <= 0) {
            return false;
        }
        return true;
    }
}
