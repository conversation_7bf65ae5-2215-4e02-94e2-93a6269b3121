# 测试法院公告分组API的PowerShell脚本

# 读取示例数据
$jsonData = Get-Content 'src/main/resources/sample-data.json' -Raw

# 测试健康检查
Write-Host "=== 测试健康检查API ===" -ForegroundColor Green
try {
    $healthResponse = Invoke-WebRequest -Uri 'http://localhost:8080/api/court-announcements/health' -Method GET
    Write-Host "状态码: $($healthResponse.StatusCode)"
    Write-Host "响应内容: $($healthResponse.Content)"
} catch {
    Write-Host "健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试数据分组API ===" -ForegroundColor Green
try {
    $groupResponse = Invoke-WebRequest -Uri 'http://localhost:8080/api/court-announcements/group' -Method POST -Body $jsonData -ContentType 'application/json'
    Write-Host "状态码: $($groupResponse.StatusCode)"
    Write-Host "响应内容:"
    $groupResponse.Content | ConvertFrom-Json | ConvertTo-Json -Depth 10
} catch {
    Write-Host "分组API测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试统计信息API ===" -ForegroundColor Green
try {
    $statsResponse = Invoke-WebRequest -Uri 'http://localhost:8080/api/court-announcements/statistics' -Method POST -Body $jsonData -ContentType 'application/json'
    Write-Host "状态码: $($statsResponse.StatusCode)"
    Write-Host "响应内容:"
    $statsResponse.Content | ConvertFrom-Json | ConvertTo-Json -Depth 10
} catch {
    Write-Host "统计API测试失败: $($_.Exception.Message)" -ForegroundColor Red
}
